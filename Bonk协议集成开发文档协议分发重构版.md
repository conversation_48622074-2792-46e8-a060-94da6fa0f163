检查一下目前的完成情况和还差什么：

  已完成的功能：

  1. ✅ Bonk协议模块结构 - 创建了完整的bonk协议目录
  2. ✅ Bonk频道订阅 - 实现了bonk_channel的Redis订阅
  3. ✅ 数据解析器 - 基于参考代码实现了BonkTradingData解析
  4. ✅ 主程序集成 - 在main.rs中启动了双频道订阅（pump + bonk）

  还需要完成的功能：

  高优先级（缺失的核心功能）：

  1. 🔴 Bonk交易构建逻辑 - 缺少实际的交易指令构建
    - 需要实现基于参考代码的Raydium Launchpad指令构建
    - 买入/卖出指令的完整账户列表
    - 指令数据的正确格式
  2. 🔴 前端API配置接口 - 缺少协议选择配置
    - 需要添加协议类型选择（pump/bonk）
    - 钱包配置中需要指定使用哪个协议
    - API接口返回协议相关信息
  3. 🔴 计算器适配 - 缺少bonk特有的计算逻辑
    - 滑点计算
    - 金额计算
    - 账户验证

  中优先级：

  4. 🟡 前端配置界面  - 需要更新前端支持协议选择
  5. 🟡 错误处理优化  - bonk特有的错误处理
  6. 🟡 日志优化  - 协议标识和日志分离

  低优先级：

  7. 🟢 性能优化  - 冷热分离架构优化
  8. 🟢 监控指标  - 分协议的统计数据

  最关键的是第1项 - Bonk交易构建逻辑，因为现在虽然能接收bonk数据，但还不能执行实际的bonk交易。需要实现完整的Raydium
  Launchpad交易指令构建流程。

  你希望我优先实现哪一项？

> 你按顺序执行吧

● 好的，我按顺序来实现。首先开始第1项：Bonk交易构建逻辑。

● Update Todos
  ⎿  ☐ 实现Bonk交易构建逻辑 - Raydium Launchpad指令构建
     ☐ 前端API配置接口 - 添加协议选择
     ☐ 计算器适配 - bonk特有的计算逻辑
     ☐ 前端配置界面 - 协议选择支持
     ☐ 错误处理优化 - bonk特有错误处理
     ☐ 性能和监控优化

● 第1项：实现Bonk交易构建逻辑

  基于参考代码，我需要在transaction_builder中添加bonk交易构建支持。先查看现有的transaction_builder：

● Read(transaction_builder.rs)
  ⎿  Read 50 lines (ctrl+r to expand)

● 继续查看transaction_builder的核心构建方法：

● Search(pattern: "pub fn build_transaction", path: "..", glob: "**/*.rs", output_mode: "content", head_limit: 10)
  ⎿  Found 0 lines

● Read(transaction_builder.rs)
  ⎿  Read 50 lines (ctrl+r to expand)

● 我看到现有的TransactionBuilder是为pump协议设计的。我需要扩展它来支持bonk协议。让我先在shared/types.rs中添加协议类型枚举：

● Update(../shared/types.rs)
  ⎿  Updated ../shared/types.rs with 9 additions
       10        FixedAmount,
       11    }
       12    
       13 +  /// 协议类型枚举
       14 +  #[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
       15 +  pub enum ProtocolType {
       16 +      /// Pump协议
       17 +      Pump,
       18 +      /// Bonk协议（Raydium Launchpad）
       19 +      Bonk,
       20 +  }
       21 +  
       22    #[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
       23    pub enum TradeType {
       24        Buy,
	   
	   
	   
	   
	   
	   
	   
	   
	   
	   
	   
	   
	   
	   当前完成状态：

  ✅ 已完成：
  1. Bonk协议模块结构创建
  2. Bonk频道订阅实现
  3. 协议类型枚举定义(ProtocolType)
  4. 基础的数据解析器

  🔴 还需要完成的关键功能：

  1. BonkTransactionBuilder - 缺少实际的交易构建器
  2. 协议选择逻辑 - WalletConfig中需要协议配置字段
  3. 前端API更新 - 支持协议选择
  4. 计算器适配 - bonk特有计算逻辑