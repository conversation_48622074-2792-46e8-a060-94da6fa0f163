use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;

// 引用通用类型
pub use crate::shared::types::{TradeType, HotPathTrade};

/// Raydium Launchpad程序ID（bonk使用）
pub const RAYDIUM_LAUNCHPAD_PROGRAM_ID: Pubkey = solana_sdk::pubkey!("LanMV9sAd7wArD4vJFi2qDdfnVhFxYSUg6eADduJ3uj");
/// WSOL Token Mint
pub const WSOL_MINT: Pubkey = solana_sdk::pubkey!("So11111111111111111111111111111111111111112");

/// Bonk交易数据结构（来自Redis的完整数据）
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct BonkTradingData {
    pub signature: String,
    pub pool_state: String,
    pub signer: String,
    pub mint_address: String,
    pub total_base_sell: u64,
    pub virtual_base: u64,
    pub virtual_quote: u64,
    pub real_base_before: u64,
    pub real_quote_before: u64,
    pub real_base_after: u64,
    pub real_quote_after: u64,
    pub amount_in: u64,
    pub amount_out: u64,
    pub protocol_fee: u64,
    pub platform_fee: u64,
    pub share_fee: u64,
    pub trade_direction: String,
    pub pool_status: String,
    pub price_before: f64,
    pub price_after: f64,
    pub slippage: f64,
    pub pool_base_vault: String,
    pub pool_quote_vault: String,
}

/// Bonk协议交易指令类型
#[allow(dead_code)]
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum BonkInstructionType {
    BuyExactIn,   // 买入指令
    SellExactIn,  // 卖出指令
    Unknown,
}

/// Bonk协议特有的热路径交易数据
#[allow(dead_code)]
#[derive(Debug, Clone)]
pub struct BonkHotPathTrade {
    // 基础信息
    pub trade_id: String,
    pub trade_type: TradeType,
    pub signature: String,
    pub sol_cost: f64,
    pub token_amount: u64,
    pub signer: String,
    pub price: f64,

    // Bonk特有的预计算账户
    pub mint_pubkey: Pubkey,
    pub pool_state: Pubkey,
    pub pool_base_vault: Pubkey,
    pub pool_quote_vault: Pubkey,
    pub authority: Pubkey,
    pub event_authority: Pubkey,
    pub global_config: Pubkey,
    pub platform_config: Pubkey,
    pub user_wsol_account: Pubkey,
    pub user_token_account: Pubkey,
}