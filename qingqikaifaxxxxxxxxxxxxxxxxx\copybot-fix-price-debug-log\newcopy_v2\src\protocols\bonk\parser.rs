use crate::protocols::bonk::{
    pda::{find_launchpad_authority, find_event_authority, get_associated_token_address},
    types::{BonkTradingData, TradeType, HotPathTrade, WSOL_MINT, RAYDIUM_LAUNCHPAD_PROGRAM_ID},
};
use solana_sdk::pubkey::Pubkey;
use std::str::FromStr;
use uuid::Uuid;
use anyhow::Result;

/// 解析Redis中的bonk交易数据
pub fn parse_bonk_trades_from_redis_bytes(
    payload: &[u8], 
    user_wallet_pubkey: &Pubkey
) -> Vec<HotPathTrade> {
    // 解析Redis消息为JSON字符串
    let json_str = match std::str::from_utf8(payload) {
        Ok(s) => s,
        Err(_) => return Vec::new(),
    };

    // 解析JSON为BonkTradingData
    let trading_data: BonkTradingData = match serde_json::from_str(json_str) {
        Ok(data) => data,
        Err(_) => return Vec::new(),
    };

    // 跳过未知Token地址的消息
    if trading_data.mint_address == "unknown" {
        return Vec::new();
    }

    // 转换为热路径交易数据
    match convert_to_hot_path_trade(&trading_data, user_wallet_pubkey) {
        Ok(hot_path_trade) => vec![hot_path_trade],
        Err(_) => Vec::new(),
    }
}

/// 将BonkTradingData转换为通用的HotPathTrade
fn convert_to_hot_path_trade(trading_data: &BonkTradingData, user_wallet_pubkey: &Pubkey) -> Result<HotPathTrade> {
    // 解析基础地址
    let mint_pubkey = Pubkey::from_str(&trading_data.mint_address)?;
    let pool_state = Pubkey::from_str(&trading_data.pool_state)?;
    let pool_base_vault = Pubkey::from_str(&trading_data.pool_base_vault)?;
    let pool_quote_vault = Pubkey::from_str(&trading_data.pool_quote_vault)?;

    // 计算PDA地址（bonk特有的创作者金库地址，这里使用pool_state作为代替）
    let creator_vault_pubkey = pool_state; // bonk使用pool state作为主要标识

    // 计算债券曲线地址（bonk使用pool_base_vault）
    let bonding_curve_pubkey = pool_base_vault;
    let associated_bonding_curve = pool_quote_vault;

    // 计算用户Token账户
    let user_ata = get_associated_token_address(user_wallet_pubkey, &mint_pubkey);

    // 确定交易类型
    let trade_type = match trading_data.trade_direction.as_str() {
        "Buy" => TradeType::Buy,
        "Sell" => TradeType::Sell,
        _ => TradeType::Unknown,
    };

    // 计算价格
    let price = if trading_data.amount_in > 0 {
        trading_data.amount_out as f64 / trading_data.amount_in as f64
    } else {
        0.0
    };

    Ok(HotPathTrade {
        trade_id: Uuid::new_v4().to_string(),
        trade_type,
        signature: trading_data.signature.clone(),
        sol_cost: trading_data.amount_in as f64 / 1_000_000_000.0, // lamports转SOL
        token_amount: trading_data.amount_out,
        signer: trading_data.signer.clone(),
        price,
        mint_pubkey,
        creator_vault_pubkey,
        bonding_curve_pubkey,
        associated_bonding_curve,
        user_ata,
        slippage_bps: 500, // 默认5%滑点
    })
}

/// 创建Bonk买入指令数据
pub fn create_bonk_buy_instruction_data(amount_in: u64, minimum_amount_out: u64) -> Vec<u8> {
    let mut data = vec![250, 234, 13, 123, 213, 156, 19, 236]; // buy_exact_in指令discriminator
    data.extend_from_slice(&amount_in.to_le_bytes());
    data.extend_from_slice(&minimum_amount_out.to_le_bytes());
    data.extend_from_slice(&0u64.to_le_bytes()); // share_fee_rate
    data
}

/// 创建Bonk卖出指令数据
pub fn create_bonk_sell_instruction_data(amount_in: u64, minimum_amount_out: u64) -> Vec<u8> {
    let mut data = vec![149, 39, 222, 155, 211, 124, 152, 26]; // sell_exact_in指令discriminator
    data.extend_from_slice(&amount_in.to_le_bytes());
    data.extend_from_slice(&minimum_amount_out.to_le_bytes());
    data.extend_from_slice(&0u64.to_le_bytes()); // share_fee_rate
    data
}