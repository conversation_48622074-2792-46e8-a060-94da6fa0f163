// This is a new file. 

use crate::hotpath::filter::{Filter, FilterCommand};
use crate::shared::constants::FILTER_UPDATE_CHANNEL;
use crate::shared::types::{TradeRecord, WalletConfig};
use crate::config::AuthConfig;
use arc_swap::ArcSwap;
use axum::{
    extract::{Path, State},
    http::StatusCode,
    response::{IntoResponse, Json, Sse},
    response::sse::Event,
    routing::{delete, get, post},
    Router,
};
use serde::{Deserialize, Serialize};
use futures_util::stream::{Stream};
use async_stream;
use redis::AsyncCommands;
use std::{collections::HashMap, net::SocketAddr, sync::{Arc, Mutex as StdMutex}, time::Duration, convert::Infallible};
use tokio::fs::File;
use tokio::io::{AsyncBufReadExt, BufReader};
use tokio::net::TcpListener;
use tokio::sync::mpsc;
use tower_http::cors::{Any, CorsLayer};
use tracing::{info, error, warn, debug};
use crate::services::price_sse_manager::PriceSseManager;
use crate::services::trade_event_manager::TradeEventManager;
use crate::services::log_sse_manager::LogSseManagerArc;
use tokio::sync::Mutex;
use tokio::fs;

const TRADE_HISTORY_FILE: &str = "trade_history.jsonl";

/// 包含API服务所需共享状态的结构体。
#[derive(Clone)]
struct ApiState {
    filter_container: Arc<ArcSwap<Filter>>,
    redis_client: redis::Client,
    price_sse_manager: Arc<StdMutex<PriceSseManager>>,
    trade_event_manager: Arc<Mutex<TradeEventManager>>,
    log_sse_manager: LogSseManagerArc,
    auth_config: AuthConfig,
}

/// 登录请求结构体
#[derive(Deserialize)]
struct LoginRequest {
    username: String,
    password: String,
}

/// 登录响应结构体
#[derive(Serialize)]
struct LoginResponse {
    success: bool,
    token: Option<String>,
    message: String,
}

/// 启动API服务器的入口函数。
///
/// # Arguments
///
/// * `filter_container` - 一个包含筛选器配置的 `Arc<ArcSwap<Filter>>`，用于在服务间共享。
/// * `redis_client` - 一个Redis客户端，用于与后端服务通信。
/// * `price_sse_manager` - SSE价格广播管理器
/// * `trade_event_manager` - SSE交易事件广播管理器
/// * `log_sse_manager` - SSE日志广播管理器
/// * `auth_config` - 认证配置
pub async fn start_api_server(
    filter_container: Arc<ArcSwap<Filter>>,
    redis_client: redis::Client,
    price_sse_manager: Arc<StdMutex<PriceSseManager>>,
    trade_event_manager: Arc<Mutex<TradeEventManager>>,
    log_sse_manager: LogSseManagerArc,
    auth_config: AuthConfig,
) {
    let state = ApiState {
        filter_container,
        redis_client,
        price_sse_manager,
        trade_event_manager,
        log_sse_manager,
        auth_config,
    };

    // 配置CORS中间件，允许所有来源的跨域请求。
    let cors = CorsLayer::new()
        .allow_methods(Any)
        .allow_origin(Any)
        .allow_headers(Any);

    // 定义API路由
    let app = Router::new()
        .route("/api/v1/auth/login", post(login_handler))
        .route(
            "/api/v1/wallets/configurations",
            get(get_wallet_configs_handler).post(upsert_wallet_config_handler),
        )
        .route(
            "/api/v1/wallets/configurations/:address",
            delete(delete_wallet_config_handler),
        )
        .route("/api/v1/trade-history", get(get_trade_history_handler))
        .route("/api/v1/prices/stream", get(price_sse_handler))
        .route("/api/v1/trades/stream", get(trade_sse_handler))
        .route("/api/v1/logs", get(get_logs_handler))
        .route("/api/v1/logs/stream", get(log_sse_handler))
        .route("/api/v1/logs/clear", post(clear_logs_handler))
        .route("/api/v1/logs/info", get(get_log_info_handler))
        .with_state(state)
        .layer(cors);

    // 绑定地址并启动服务器
    let addr = SocketAddr::from(([127, 0, 0, 1], 8080));
    info!("API服务器正在监听 http://{}", addr);

    let listener = match TcpListener::bind(addr).await {
        Ok(listener) => listener,
        Err(e) => {
            error!("API服务器无法绑定到地址 {}: {}", addr, e);
            return;
        }
    };
    
    if let Err(e) = axum::serve(listener, app).await {
        error!("API服务器遇到致命错误: {}", e);
    }
}

/// 处理 `POST /api/v1/auth/login` 请求的处理器。
async fn login_handler(
    State(state): State<ApiState>,
    Json(payload): Json<LoginRequest>,
) -> impl IntoResponse {
    info!("收到登录请求，用户名: {}", payload.username);

    if state.auth_config.validate_user(&payload.username, &payload.password) {
        let token = format!("simple_token_{}", uuid::Uuid::new_v4());
        info!("用户 {} 登录成功", payload.username);

        let response = LoginResponse {
            success: true,
            token: Some(token),
            message: "登录成功".to_string(),
        };

        (StatusCode::OK, Json(response)).into_response()
    } else {
        warn!("用户 {} 登录失败：用户名或密码错误", payload.username);

        let response = LoginResponse {
            success: false,
            token: None,
            message: "用户名或密码错误".to_string(),
        };

        (StatusCode::UNAUTHORIZED, Json(response)).into_response()
    }
}

/// 处理 `GET /api/v1/trade-history` 请求的处理器。
/// 它会读取交易历史文件并以JSON数组的形式返回。
async fn get_trade_history_handler() -> impl IntoResponse {
    info!("收到获取交易历史记录的API请求");
    match File::open(TRADE_HISTORY_FILE).await {
        Ok(file) => {
            let reader = BufReader::new(file);
            let mut lines = reader.lines();
            let mut records = Vec::new();

            while let Some(line) = lines.next_line().await.unwrap_or(None) {
                match serde_json::from_str::<TradeRecord>(&line) {
                    Ok(record) => records.push(record),
                    Err(e) => {
                        warn!("解析历史记录文件中的无效行: {}, line: '{}'", e, line);
                    }
                }
            }
            (StatusCode::OK, Json(records)).into_response()
        }
        Err(e) => {
            if e.kind() == std::io::ErrorKind::NotFound {
                info!("交易历史文件不存在，返回空列表。");
                (StatusCode::OK, Json(Vec::<TradeRecord>::new())).into_response()
            } else {
                error!("读取交易历史文件失败: {}", e);
                (StatusCode::INTERNAL_SERVER_ERROR, "读取历史记录失败").into_response()
            }
        }
    }
}

/// 处理 `DELETE /api/v1/wallets/configurations/:address` 请求的处理器。
/// 它接收一个钱包地址作为路径参数，并发布一个删除命令到Redis。
async fn delete_wallet_config_handler(
    State(state): State<ApiState>,
    Path(address): Path<String>,
) -> impl IntoResponse {
    info!("收到删除钱包配置请求: {}", address);

    let command = FilterCommand::RemoveWallet(address);
    let command_json = match serde_json::to_string(&command) {
        Ok(json) => json,
        Err(e) => {
            error!("序列化钱包删除命令失败: {}", e);
            return (StatusCode::INTERNAL_SERVER_ERROR, "命令序列化失败").into_response();
        }
    };

    let mut conn = match state.redis_client.get_multiplexed_async_connection().await {
        Ok(conn) => conn,
        Err(e) => {
            error!("无法从API服务器获取Redis连接: {}", e);
            return (StatusCode::SERVICE_UNAVAILABLE, "无法连接到后端服务").into_response();
        }
    };

    match conn
        .publish::<&str, &str, i32>(FILTER_UPDATE_CHANNEL, &command_json)
        .await
    {
        Ok(_) => {
            info!("钱包配置删除命令已成功发布到Redis频道。");
            (StatusCode::ACCEPTED, "配置删除请求已接受").into_response()
        }
        Err(e) => {
            error!("向Redis发布钱包删除命令失败: {}", e);
            (StatusCode::INTERNAL_SERVER_ERROR, "发布命令失败").into_response()
        }
    }
}

/// 处理 `POST /api/v1/wallets/configurations` 请求的处理器。
/// 它接收一个钱包配置，并发布一个更新命令到Redis。
async fn upsert_wallet_config_handler(
    State(state): State<ApiState>,
    Json(mut payload): Json<WalletConfig>,
) -> impl IntoResponse {
    info!("收到钱包配置更新请求，钱包地址: {}", payload.wallet_address);

    // 调试：打印接收到的原始数据
    info!("🔍 接收到的原始配置数据:");
    debug!("  follow_mode: {:?}", payload.follow_mode);
    debug!("  fixed_follow_amount_sol: {:?}", payload.fixed_follow_amount_sol);
    debug!("  follow_percentage: {:?}", payload.follow_percentage);
    debug!("  min_price_multiplier: {:?}", payload.min_price_multiplier);
    debug!("  max_price_multiplier: {:?}", payload.max_price_multiplier);
    debug!("  protocol_type: {:?}", payload.protocol_type);
    debug!("  bonk_enabled: {:?}", payload.bonk_enabled);
    debug!("  pump_enabled: {:?}", payload.pump_enabled);

    // 验证跟单配置的一致性
    if let Err(validation_error) = payload.validate_follow_config() {
        warn!("钱包配置验证失败: {}", validation_error);
        return (StatusCode::BAD_REQUEST, format!("配置验证失败: {}", validation_error)).into_response();
    }

    // 规范化配置，确保只有对应模式的字段有值
    payload = payload.normalize();

    // 格式化显示配置信息
    info!("钱包配置已更新:");
    info!("✅ 钱包地址: {}", payload.wallet_address);
    info!("✅ 启用状态: {}", if payload.is_active { "已启用" } else { "已禁用" });
    info!("✅ 协议类型: {:?}", payload.protocol_type);

    // 跟单模式信息
    match payload.follow_mode {
        crate::shared::types::FollowMode::Percentage => {
            info!("✅ 跟单模式: 百分比跟单 (Percentage)");
            if let Some(pct) = payload.follow_percentage {
                let ratio_text = if pct == 100.0 {
                    "1:1".to_string()
                } else {
                    format!("1:{}", pct/100.0)
                };
                info!("✅ 跟单比例: {}% ({}跟单)", pct, ratio_text);
            }
        },
        crate::shared::types::FollowMode::FixedAmount => {
            info!("✅ 跟单模式: 固定金额跟单 (FixedAmount)");
            if let Some(amount) = payload.fixed_follow_amount_sol {
                info!("✅ 固定跟单金额: {} SOL", amount);
            }
        }
    }

    // 筛选范围
    let min_sol = payload.sol_amount_min.map(|v| format!("{}", v)).unwrap_or_else(|| "无限制".to_string());
    let max_sol = payload.sol_amount_max.map(|v| format!("{}", v)).unwrap_or_else(|| "无限制".to_string());
    info!("✅ 筛选范围: 被跟随者交易金额 {}-{} SOL", min_sol, max_sol);

    // 价格筛选
    let min_price = payload.min_price_multiplier.map(|v| format!("{}", v)).unwrap_or_else(|| "无限制".to_string());
    let max_price = payload.max_price_multiplier.map(|v| format!("{}", v)).unwrap_or_else(|| "无限制".to_string());
    info!("✅ 价格筛选: 代币价格 {}-{} 范围内", min_price, max_price);

    // 止盈策略
    if let Some(strategy) = &payload.take_profit_strategy {
        match strategy.as_str() {
            "exponential" => {
                if payload.exponential_sell_trigger_step_pct.is_none() {
                    return (StatusCode::BAD_REQUEST, "指数策略需要设置 exponential_sell_trigger_step_pct".to_string()).into_response();
                }
                if payload.exponential_sell_base_portion_pct.is_none() {
                    return (StatusCode::BAD_REQUEST, "指数策略需要设置 exponential_sell_base_portion_pct".to_string()).into_response();
                }
                if payload.exponential_sell_power.is_none() {
                    return (StatusCode::BAD_REQUEST, "指数策略需要设置 exponential_sell_power".to_string()).into_response();
                }
                info!("✅ 指数加码卖出: 触发间隔={}%, 基础比例={}%, 幂={}", 
                    payload.exponential_sell_trigger_step_pct.unwrap(),
                    payload.exponential_sell_base_portion_pct.unwrap(),
                    payload.exponential_sell_power.unwrap()
                );
            },
            "trailing" => {
                let trailing = payload.trailing_stop_profit_percentage.unwrap_or(0.0);
                info!("✅ 止盈策略: 追踪止盈，{}%回撤", trailing);
            },
            "standard" => {
                let start = payload.take_profit_start_pct.unwrap_or(0.0);
                let step = payload.take_profit_step_pct.unwrap_or(0.0);
                let portion = payload.take_profit_sell_portion_pct.unwrap_or(0.0);
                info!("✅ 止盈策略: 标准分步止盈，{}%启动，{}%间隔，{}%卖出", start, step, portion);
            },
            "volatility" => {
                // 波动率策略可以使用默认值，不强制要求所有参数
                info!("✅ 波动率突破策略: 布林带窗口={}, σ={:.1}, ATR样本={}, ATR倍数={:.1}, 卖出比例={}%, 冷却={}ms, 最小卖出/仓位阈值={}%",
                    payload.volatility_bb_window_size.unwrap_or(80),
                    payload.volatility_bb_stddev.unwrap_or(2.0),
                    payload.volatility_atr_samples.unwrap_or(30),
                    payload.volatility_atr_multiplier.unwrap_or(1.5),
                    payload.volatility_sell_percent.unwrap_or(40.0),
                    payload.volatility_cooldown_ms.unwrap_or(300),
                    payload.min_partial_sell_pct.unwrap_or(0.0)
                );
                
                // 验证参数范围
                if let Some(window_size) = payload.volatility_bb_window_size {
                    if window_size == 0 || window_size > 10000 {
                        return (StatusCode::BAD_REQUEST, "volatility_bb_window_size 必须在 1-10000 之间".to_string()).into_response();
                    }
                }
                if let Some(stddev) = payload.volatility_bb_stddev {
                    if stddev <= 0.0 || stddev > 5.0 {
                        return (StatusCode::BAD_REQUEST, "volatility_bb_stddev 必须在 0-5 之间".to_string()).into_response();
                    }
                }
                if let Some(samples) = payload.volatility_atr_samples {
                    if samples == 0 || samples > 1000 {
                        return (StatusCode::BAD_REQUEST, "volatility_atr_samples 必须在 1-1000 之间".to_string()).into_response();
                    }
                }
                if let Some(multiplier) = payload.volatility_atr_multiplier {
                    if multiplier <= 0.0 || multiplier > 5.0 {
                        return (StatusCode::BAD_REQUEST, "volatility_atr_multiplier 必须在 0-5 之间".to_string()).into_response();
                    }
                }
                if let Some(sell_pct) = payload.volatility_sell_percent {
                    if sell_pct <= 0.0 || sell_pct > 100.0 {
                        return (StatusCode::BAD_REQUEST, "volatility_sell_percent 必须在 0-100 之间".to_string()).into_response();
                    }
                }
                if let Some(cooldown) = payload.volatility_cooldown_ms {
                    if cooldown > 60000 {
                        return (StatusCode::BAD_REQUEST, "volatility_cooldown_ms 不能超过 60000 毫秒".to_string()).into_response();
                    }
                }
            },
            _ => info!("✅ 止盈策略: {}", strategy),
        }
    } else {
        info!("✅ 止盈策略: 未设置");
    }

    // 风险控制
    let hard_stop = payload.hard_stop_loss_pct.map(|v| format!("{}%硬止损", v)).unwrap_or_else(|| "无硬止损".to_string());
    let callback_stop = payload.callback_stop_pct.map(|v| format!("{}%回调止损", v)).unwrap_or_else(|| "无回调止损".to_string());
    info!("✅ 风险控制: {}，{}", hard_stop, callback_stop);

    // 交易参数（买入 / 卖出）
    let buy_tip = payload.accelerator_tip_percentage.unwrap_or(1.0);
    let sell_tip = payload.sell_tip_percentage.unwrap_or(buy_tip);
    info!(
        "✅ 交易参数: 买入[滑点{:.2}%, tip{:.2}%, 优先费{}, CU{}] | 卖出[滑点{:.2}%, tip{:.2}%, 优先费{}, CU{}]",
        payload.slippage_percentage,
        buy_tip,
        payload.priority_fee,
        payload.compute_unit_limit,
        payload.sell_slippage_percentage.unwrap_or(payload.slippage_percentage),
        sell_tip,
        payload.sell_priority_fee.unwrap_or(payload.priority_fee),
        payload.sell_compute_unit_limit.unwrap_or(payload.compute_unit_limit)
    );

    // 动态持仓配置
    if let Some(entry_ms) = payload.entry_confirmation_ms {
        let trigger_pct = payload.dynamic_hold_trigger_pct.unwrap_or(0.0);
        let extend_ms = payload.dynamic_hold_extend_ms.unwrap_or(0);
        let max_ms = payload.dynamic_hold_max_ms.unwrap_or(0);
        info!("✅ 动态持仓: {}ms初始确认，{}%触发延长，每次延长{}ms，最长{}ms",
            entry_ms, trigger_pct, extend_ms, max_ms);
    } else {
        info!("✅ 动态持仓: 未配置");
    }

    // 自动暂停配置
    if let Some(auto_config) = &payload.auto_suspend_config {
        if auto_config.enabled {
            info!("✅ 自动暂停: 已启用，监控最近{}笔交易，{}笔亏损超过{}%时暂停",
                auto_config.window_size,
                auto_config.loss_count,
                auto_config.loss_threshold
            );
        } else {
            info!("✅ 自动暂停: 已禁用");
        }
    } else {
        info!("✅ 自动暂停: 未配置");
    }

    // 卖出重试配置
    info!("✅ 卖出重试: 最大{}次重试，每次增加{}%滑点",
        payload.sell_retry_max_attempts,
        payload.sell_retry_slippage_increment
    );

    let command = FilterCommand::UpdateWallet(payload);
    let command_json = match serde_json::to_string(&command) {
        Ok(json) => json,
        Err(e) => {
            error!("序列化钱包更新命令失败: {}", e);
            return (StatusCode::INTERNAL_SERVER_ERROR, "命令序列化失败").into_response();
        }
    };

    let mut conn = match state.redis_client.get_multiplexed_async_connection().await {
        Ok(conn) => conn,
        Err(e) => {
            error!("无法从API服务器获取Redis连接: {}", e);
            return (StatusCode::SERVICE_UNAVAILABLE, "无法连接到后端服务").into_response();
        }
    };

    match conn
        .publish::<&str, &str, i32>(FILTER_UPDATE_CHANNEL, &command_json)
        .await
    {
        Ok(_) => {
            info!("钱包配置更新命令已成功发布到Redis频道。");
            (StatusCode::ACCEPTED, "配置更新请求已接受").into_response()
        }
        Err(e) => {
            error!("向Redis发布钱包更新命令失败: {}", e);
            (StatusCode::INTERNAL_SERVER_ERROR, "发布命令失败").into_response()
        }
    }
}

/// 处理 `GET /api/v1/wallets/configurations` 请求的处理器。
/// 它会返回当前所有钱包的配置信息。
async fn get_wallet_configs_handler(
    State(state): State<ApiState>,
) -> impl IntoResponse {
    info!("收到获取所有钱包配置的API请求");
    // `load_full` 是一个无锁操作，可以安全地获取当前配置的Arc指针。
    let filter = state.filter_container.load_full();
    
    // 克隆HashMap，以便在不持有任何锁或引用时将其序列化。
    let wallet_configs: HashMap<String, WalletConfig> = filter.config.wallet_configs.clone();
    
    (StatusCode::OK, Json(wallet_configs))
}

async fn price_sse_handler(
    State(state): State<ApiState>,
) -> Sse<impl Stream<Item = Result<Event, Infallible>>> {
    let (tx, mut rx) = mpsc::channel(128);

    // 将发送端注册到价格管理器中
    state
        .price_sse_manager
        .lock()
        .unwrap_or_else(|p| p.into_inner())
        .register(tx);

    let stream = async_stream::stream! {
        // 每15秒发送一个心跳信号以保持连接活跃
        let mut interval = tokio::time::interval(Duration::from_secs(15));
        loop {
            tokio::select! {
                // 从广播通道接收价格消息
                Some(msg) = rx.recv() => {
                    yield msg;
                }
                // 发送心跳
                _ = interval.tick() => {
                    yield Ok(Event::default().comment("keep-alive"));
                }
                // 如果两个通道都关闭则退出
                else => {
                    break;
                }
            }
        }
    };

    Sse::new(stream).keep_alive(
        axum::response::sse::KeepAlive::new()
            .interval(Duration::from_secs(15))
            .text("keep-alive-text"),
    )
}

async fn trade_sse_handler(
    State(state): State<ApiState>,
) -> Sse<impl Stream<Item = Result<Event, Infallible>>> {
    let (tx, mut rx) = mpsc::channel(128);

    // 将发送端注册到交易事件管理器中
    {
        let mut mgr = state.trade_event_manager.lock().await;
        mgr.register(tx);
    }

    let stream = async_stream::stream! {
        // 每15秒发送一个心跳信号以保持连接活跃
        let mut interval = tokio::time::interval(Duration::from_secs(15));
        loop {
            tokio::select! {
                // 从广播通道接收交易事件消息
                Some(msg) = rx.recv() => {
                    yield msg;
                }
                // 发送心跳
                _ = interval.tick() => {
                    yield Ok(Event::default().comment("keep-alive"));
                }
                // 如果两个通道都关闭则退出
                else => {
                    break;
                }
            }
        }
    };

    Sse::new(stream).keep_alive(
        axum::response::sse::KeepAlive::new()
            .interval(Duration::from_secs(15))
            .text("keep-alive-text"),
    )
}

/// 处理 `GET /api/v1/logs` 请求。
/// 返回一个包含所有日志文件名及其内容的JSON对象。
async fn get_logs_handler() -> impl IntoResponse {
    info!("收到获取所有日志内容的API请求");
    let mut logs = HashMap::new();
    let log_dir = "logs";

    let mut read_dir = match fs::read_dir(log_dir).await {
        Ok(read_dir) => read_dir,
        Err(e) => {
            error!("读取日志目录 '{}' 失败: {}", log_dir, e);
            return (StatusCode::INTERNAL_SERVER_ERROR, "无法访问日志目录").into_response();
        }
    };

    while let Some(entry) = read_dir.next_entry().await.unwrap_or(None) {
        let path = entry.path();
        if path.is_file() {
            if let Some(filename_os) = path.file_name() {
                if let Some(filename) = filename_os.to_str() {
                    match fs::read_to_string(&path).await {
                        Ok(content) => {
                            logs.insert(filename.to_string(), content);
                        }
                        Err(e) => {
                            warn!("读取日志文件 '{}' 失败: {}", filename, e);
                        }
                    }
                }
            }
        }
    }

    (StatusCode::OK, Json(logs)).into_response()
}

/// 处理 `GET /api/v1/logs/stream` 请求。
/// 返回日志的SSE流
async fn log_sse_handler(
    State(state): State<ApiState>,
) -> Sse<impl Stream<Item = Result<Event, Infallible>>> {
    let (tx, mut rx) = mpsc::channel(128);

    // 将发送端注册到日志管理器中
    {
        let mut mgr = state.log_sse_manager.lock().unwrap_or_else(|p| p.into_inner());
        mgr.register(tx);
    }

    let stream = async_stream::stream! {
        // 每15秒发送一个心跳信号以保持连接活跃
        let mut interval = tokio::time::interval(Duration::from_secs(15));
        loop {
            tokio::select! {
                // 从广播通道接收日志消息
                Some(msg) = rx.recv() => {
                    yield msg;
                }
                // 发送心跳
                _ = interval.tick() => {
                    yield Ok(Event::default().comment("keep-alive"));
                }
                // 如果两个通道都关闭则退出
                else => {
                    break;
                }
            }
        }
    };

    Sse::new(stream).keep_alive(
        axum::response::sse::KeepAlive::new()
            .interval(Duration::from_secs(15))
            .text("keep-alive-text"),
    )
}

/// 处理 `POST /api/v1/logs/clear` 请求。
/// 清除系统日志缓存和日志文件
async fn clear_logs_handler(
    State(state): State<ApiState>,
) -> impl IntoResponse {
    info!("收到清除日志请求");

    // 清除SSE缓存
    {
        let mut mgr = state.log_sse_manager.lock().unwrap_or_else(|p| p.into_inner());
        mgr.clear_logs();
        info!("已清除日志SSE缓存");
    }

    // 清除日志文件
    let log_dir = "logs";
    match fs::read_dir(log_dir).await {
        Ok(mut read_dir) => {
            let mut cleared_files = Vec::new();
            
            while let Some(entry) = read_dir.next_entry().await.unwrap_or(None) {
                let path = entry.path();
                if path.is_file() {
                    if let Some(filename) = path.file_name().and_then(|n| n.to_str()) {
                        match fs::remove_file(&path).await {
                            Ok(_) => {
                                cleared_files.push(filename.to_string());
                            }
                            Err(e) => {
                                warn!("删除日志文件 '{}' 失败: {}", filename, e);
                            }
                        }
                    }
                }
            }
            
            if cleared_files.is_empty() {
                info!("没有找到需要清除的日志文件");
                (StatusCode::OK, "日志缓存已清除，没有找到日志文件").into_response()
            } else {
                info!("已清除日志文件: {:?}", cleared_files);
                (StatusCode::OK, format!("已清除日志缓存和 {} 个日志文件", cleared_files.len())).into_response()
            }
        }
        Err(e) => {
            if e.kind() == std::io::ErrorKind::NotFound {
                info!("日志目录不存在，仅清除缓存");
                (StatusCode::OK, "日志缓存已清除，日志目录不存在").into_response()
            } else {
                error!("访问日志目录失败: {}", e);
                (StatusCode::INTERNAL_SERVER_ERROR, "清除日志文件失败").into_response()
            }
        }
    }
}

/// 处理 `GET /api/v1/logs/info` 请求。
/// 返回日志系统配置信息
async fn get_log_info_handler(
    State(state): State<ApiState>,
) -> impl IntoResponse {
    let log_info = crate::logging::get_log_info();
    
    // 添加当前状态信息
    let mut info = log_info.as_object().unwrap().clone();
    
    // 获取SSE管理器状态
    if let Ok(mgr) = state.log_sse_manager.lock() {
        info.insert("sse_clients".to_string(), serde_json::json!(mgr.client_count()));
        info.insert("cached_logs".to_string(), serde_json::json!(mgr.cached_log_count()));
    }
    
    (StatusCode::OK, Json(info)).into_response()
} 