use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;

// 引用通用类型
pub use crate::shared::types::{TradeType, HotPathTrade};

/// Pump协议专用常量
pub const PUMP_FUN_PROGRAM_ID: Pubkey = solana_sdk::pubkey!("6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P");

/// Pump协议交易指令类型
#[allow(dead_code)]  // 暂时未使用，后续扩展时会用到
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum PumpInstructionType {
    Create,
    Buy,
    Sell,
    Unknown,
}

/// Pump协议特有的交易数据
#[allow(dead_code)]  // 暂时未使用，后续扩展时会用到
#[derive(Debug, Clone)]
pub struct PumpTradeData {
    pub mint: Pubkey,
    pub bonding_curve: Pubkey,
    pub associated_bonding_curve: Pubkey,
    pub creator_vault: Pubkey,
    pub user_ata: Pubkey,
    pub instruction_type: PumpInstructionType,
}