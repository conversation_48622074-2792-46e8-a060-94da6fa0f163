use solana_sdk::pubkey::Pubkey;
use crate::protocols::bonk::types::RAYDIUM_LAUNCHPAD_PROGRAM_ID;

/// 计算Raydium Launchpad权限地址
/// Seed: "vault_auth_seed"
pub fn find_launchpad_authority() -> (Pubkey, u8) {
    Pubkey::find_program_address(
        &[b"vault_auth_seed"],
        &RAYDIUM_LAUNCHPAD_PROGRAM_ID,
    )
}

/// 计算Raydium Launchpad事件权限地址
/// Seed: "__event_authority"
pub fn find_event_authority() -> (Pubkey, u8) {
    Pubkey::find_program_address(
        &[b"__event_authority"],
        &RAYDIUM_LAUNCHPAD_PROGRAM_ID,
    )
}

/// 获取关联Token账户地址
pub fn get_associated_token_address(wallet: &Pubkey, token_mint: &Pubkey) -> Pubkey {
    spl_associated_token_account::get_associated_token_address(wallet, token_mint)
}