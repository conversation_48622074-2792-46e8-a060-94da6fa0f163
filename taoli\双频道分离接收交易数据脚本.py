#!/usr/bin/env python3
"""
测试脚本：验证taoli系统的频道配置
"""

import redis
import json
import time
import threading
from datetime import datetime

def current_time():
    return datetime.now().strftime("%H:%M:%S.%f")[:-3]

class ChannelListener:
    def __init__(self, redis_host='127.0.0.1', redis_port=6379):
        self.redis_client = redis.Redis(host=redis_host, port=redis_port, decode_responses=True)
        self.pubsub = self.redis_client.pubsub()
        self.running = False
        
    def listen_to_channels(self, channels):
        """监听多个频道"""
        print(f"[{current_time()}] 开始监听频道: {', '.join(channels)}")
        
        # 订阅所有频道
        for channel in channels:
            self.pubsub.subscribe(channel)
            print(f"[{current_time()}] 已订阅频道: {channel}")
        
        self.running = True
        msg_count = 0
        
        try:
            for message in self.pubsub.listen():
                if not self.running:
                    break
                
                # 只处理消息类型为'message'的数据
                if message['type'] == 'message':
                    msg_count += 1
                    channel = message['channel']
                    data = message['data']
                    
                    # 打印消息头信息
                    print(f"\n{'='*80}")
                    print(f"[{current_time()}] 收到消息 #{msg_count} 从频道: {channel}")
                    print(f"{'-'*80}")
                    
                    # 尝试解析JSON，如果不是JSON则直接打印
                    try:
                        json_data = json.loads(data)
                        print(json.dumps(json_data, ensure_ascii=False, indent=2))
                    except json.JSONDecodeError:
                        print(data)
                    
                    print(f"{'='*80}\n")
        except KeyboardInterrupt:
            print(f"\n[{current_time()}] 接收到中断信号，停止监听")
        except Exception as e:
            print(f"\n[{current_time()}] 监听时发生错误: {e}")
        finally:
            self.stop()
    
    def stop(self):
        """停止监听"""
        self.running = False
        self.pubsub.close()
        print(f"[{current_time()}] 已停止监听")

def main():
    print("=== taoli系统频道配置测试 ===")
    print(f"[{current_time()}] 启动频道监听器...")
    
    # 定义要监听的频道
    channels = [
        'bonk_channel',    # Bonk交易频道
        'pump_channel',    # Pump交易频道
        'trades_channel'   # 原始频道（向后兼容）
    ]
    
    listener = ChannelListener()
    
    try:
        listener.listen_to_channels(channels)
    except KeyboardInterrupt:
        print(f"\n[{current_time()}] 程序被用户中断")
    except Exception as e:
        print(f"\n[{current_time()}] 程序异常: {e}")
    finally:
        listener.stop()

if __name__ == "__main__":
    main()
